<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RONIN Blob Storage Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>RONIN Blob Storage Test</h1>
    <p>This page tests if RONIN blob storage is working correctly.</p>
    
    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
        <p>Click here or drag and drop a file to test upload</p>
        <input type="file" id="fileInput" style="display: none;" accept="image/*">
    </div>
    
    <button onclick="testUpload()">Test Upload to RONIN</button>
    
    <div id="result"></div>

    <script>
        let selectedFile = null;

        document.getElementById('fileInput').addEventListener('change', function(e) {
            selectedFile = e.target.files[0];
            if (selectedFile) {
                document.querySelector('.upload-area p').textContent = `Selected: ${selectedFile.name}`;
            }
        });

        async function testUpload() {
            if (!selectedFile) {
                showResult('Please select a file first', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('file', selectedFile);

            try {
                showResult('Uploading...', 'info');
                
                const response = await fetch('/api/test-upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (response.ok) {
                    showResult(`✅ Success: ${result.message}`, 'success');
                    console.log('Upload result:', result);
                } else {
                    showResult(`❌ Error: ${result.error}`, 'error');
                    console.error('Upload error:', result);
                }
            } catch (error) {
                showResult(`❌ Network Error: ${error.message}`, 'error');
                console.error('Network error:', error);
            }
        }

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
        }

        // Drag and drop functionality
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#007bff';
            uploadArea.style.backgroundColor = '#f8f9fa';
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = 'transparent';
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = 'transparent';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                selectedFile = files[0];
                document.querySelector('.upload-area p').textContent = `Selected: ${selectedFile.name}`;
            }
        });
    </script>
</body>
</html>
