// pages/teacher/[slug]/students.tsx
import { use, useBatch } from 'blade/server/hooks';
import TeacherStudentsPageWithData from '../../../components/teacher/TeacherStudentsPageWithData.client';

const TeachersStudentsPage = () => {
  // OPTIMIZATION: Use more targeted RONIN queries to reduce revalidation frequency
  // Instead of fetching ALL records, fetch only what we need for this specific page
  const [teacherUsers, studentUsers, allGradeLevels, allClasses, allStudentTeachers] = useBatch(() => {
    // Fetch only teacher users with only the fields we need (much smaller dataset)
    const teachers = use.users({
      with: { role: 'teacher' },
      selecting: ['id', 'name', 'email', 'slug', 'role', 'isVerified', 'createdAt']
    });

    // Fetch only student users with only the fields we need (smaller than all users)
    const students = use.users({
      with: { role: 'student' },
      selecting: ['id', 'name', 'email', 'slug', 'role', 'teacherId', 'isActive', 'classId', 'grade', 'username', 'createdAt']
    });

    // Try to fetch grade levels with only needed fields
    let gradeLevels: any[] = [];
    try {
      gradeLevels = use.gradeLevels({
        selecting: ['id', 'name', 'teacherId', 'isActive', 'createdAt']
      }) || [];
    } catch (error) {
      // Model not yet migrated, silently use empty array
      gradeLevels = [];
    }

    // Try to fetch classes with only needed fields
    let classes: any[] = [];
    try {
      classes = use.classes({
        selecting: ['id', 'name', 'teacherId', 'isActive', 'gradeLevel', 'createdAt']
      }) || [];
    } catch (error) {
      // Model not yet migrated, silently use empty array
      classes = [];
    }

    // Fetch student-teacher relationships with only needed fields
    let studentTeachers: any[] = [];
    try {
      studentTeachers = use.studentTeachers({
        selecting: ['id', 'studentId', 'teacherId', 'status', 'createdAt']
      }) || [];
    } catch (error) {
      // Model not yet migrated, silently use empty array
      studentTeachers = [];
    }

    return [teachers || [], students || [], gradeLevels, classes, studentTeachers];
  });

  // Combine teacher and student users for backward compatibility
  const allUsers = [...teacherUsers, ...studentUsers];

  return (
    <TeacherStudentsPageWithData
      allUsers={allUsers}
      allGradeLevels={allGradeLevels}
      allClasses={allClasses}
      allStudentTeachers={allStudentTeachers}
    />
  );
};

// Export as default for Blade framework
export default TeachersStudentsPage;
