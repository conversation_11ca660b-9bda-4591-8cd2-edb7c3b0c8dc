// components/optimization/RevalidationController.client.tsx
'use client';

import { useEffect, useRef } from 'react';
import { useLocation } from 'blade/hooks';

interface RevalidationControllerProps {
  children: React.ReactNode;
  pageName: string;
}

/**
 * Component to help monitor and control revalidation behavior
 * This helps us understand when and why pages are revalidating
 */
export const RevalidationController: React.FC<RevalidationControllerProps> = ({
  children,
  pageName
}) => {
  const location = useLocation();
  const lastRevalidationRef = useRef<number>(0);
  const revalidationCountRef = useRef<number>(0);
  const isActivePageRef = useRef<boolean>(true);

  // Track when this page becomes active/inactive
  useEffect(() => {
    const currentPath = location.pathname;
    const isCurrentlyActive = currentPath.includes(pageName.toLowerCase());
    
    if (isCurrentlyActive !== isActivePageRef.current) {
      isActivePageRef.current = isCurrentlyActive;
      console.log(`📍 [${pageName}] Page ${isCurrentlyActive ? 'ACTIVATED' : 'DEACTIVATED'} - Path: ${currentPath}`);
    }
  }, [location.pathname, pageName]);

  // Monitor revalidation frequency
  useEffect(() => {
    const now = Date.now();
    const timeSinceLastRevalidation = now - lastRevalidationRef.current;
    revalidationCountRef.current += 1;

    if (lastRevalidationRef.current > 0) {
      const isActive = isActivePageRef.current;
      const status = isActive ? '🟢 ACTIVE' : '🔴 INACTIVE';

      console.log(`🔄 [${pageName}] ${status} - Revalidation #${revalidationCountRef.current} - ${timeSinceLastRevalidation}ms since last`);

      // Alert if revalidating too frequently
      if (timeSinceLastRevalidation < 3000) {
        console.warn(`⚠️ [${pageName}] Revalidating too frequently! Only ${timeSinceLastRevalidation}ms since last revalidation`);

        // Add stack trace to help identify the source
        console.trace(`[${pageName}] Revalidation source trace:`);
      }

      // Alert if inactive page is revalidating
      if (!isActive) {
        console.warn(`⚠️ [${pageName}] INACTIVE page is revalidating - this may indicate unnecessary background revalidation`);
      }
    }

    lastRevalidationRef.current = now;
  });

  // Monitor window focus events that might trigger revalidation
  useEffect(() => {
    let focusTimeout: NodeJS.Timeout;
    let blurTimeout: NodeJS.Timeout;

    const handleFocus = () => {
      // Clear any pending blur timeout
      if (blurTimeout) clearTimeout(blurTimeout);

      // Debounce focus events to avoid spam
      focusTimeout = setTimeout(() => {
        const isActive = isActivePageRef.current;
        const status = isActive ? '🟢 ACTIVE' : '🔴 INACTIVE';
        console.log(`🔍 [${pageName}] ${status} Window focused - Blade may trigger revalidation`);
      }, 100);
    };

    const handleBlur = () => {
      // Clear any pending focus timeout
      if (focusTimeout) clearTimeout(focusTimeout);

      // Debounce blur events to avoid spam
      blurTimeout = setTimeout(() => {
        const isActive = isActivePageRef.current;
        const status = isActive ? '🟢 ACTIVE' : '🔴 INACTIVE';
        console.log(`😴 [${pageName}] ${status} Window blurred`);
      }, 100);
    };

    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);

    return () => {
      if (focusTimeout) clearTimeout(focusTimeout);
      if (blurTimeout) clearTimeout(blurTimeout);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
    };
  }, [pageName]);

  return <>{children}</>;
};

export default RevalidationController;
