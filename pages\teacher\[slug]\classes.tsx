// pages/teacher/[slug]/classes.tsx
import { use, useBatch } from 'blade/server/hooks';
import TeacherClassesPageWithData from '../../../components/teacher/TeacherClassesPageWithData.client';

const TeachersClassesPage = () => {
  // OPTIMIZATION: Use more targeted RONIN queries to reduce revalidation frequency
  // The classes page doesn't need user data, so we can avoid that large query entirely
  const [allClasses, allGradeLevels, allEducationalContexts] = useBatch(() => {
    // Try to fetch data, but handle the case where models don't exist yet
    let classes: any[] = [];
    let gradeLevels: any[] = [];
    let educationalContexts: any[] = [];

    try {
      // Fetch classes with only needed fields to reduce data transfer
      classes = use.classes({
        selecting: ['id', 'name', 'teacherId', 'isActive', 'gradeLevel', 'subject', 'description', 'createdAt']
      }) || [];
    } catch (error) {
      // Model not yet migrated, silently use empty array
      classes = [];
    }

    try {
      // Fetch grade levels with only needed fields to reduce data transfer
      gradeLevels = use.gradeLevels({
        selecting: ['id', 'name', 'teacherId', 'isActive', 'educationalContext', 'createdAt']
      }) || [];
    } catch (error) {
      // Model not yet migrated, silently use empty array
      gradeLevels = [];
    }

    try {
      // Fetch educational contexts with only needed fields to reduce data transfer
      educationalContexts = use.educationalContexts({
        selecting: ['id', 'name', 'teacherId', 'isActive', 'description', 'createdAt']
      }) || [];
    } catch (error) {
      // Model not yet migrated, silently use empty array
      educationalContexts = [];
    }

    return [classes, gradeLevels, educationalContexts];
  });

  return (
    <TeacherClassesPageWithData
      allClasses={allClasses}
      allGradeLevels={allGradeLevels}
      allEducationalContexts={allEducationalContexts}
    />
  );
};

export default TeachersClassesPage;
