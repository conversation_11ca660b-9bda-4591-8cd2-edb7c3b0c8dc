// triggers/studentTeacher.ts
import type { AddTrigger, SetTrigger, GetTrigger, RemoveTrigger } from 'blade/types';

// Trigger for creating student-teacher relationships
export const add: AddTrigger = (query) => {
  // Ensure query.with exists
  if (!query.with) {
    return query;
  }

  // Handle both single object and array cases
  const processRelationshipData = (relationshipData: any) => {
    // Set default status if not provided
    if (!relationshipData.status) {
      relationshipData.status = 'active';
    }

    // Set default assignedAt if not provided
    if (!relationshipData.assignedAt) {
      relationshipData.assignedAt = new Date();
    }

    // Validate required fields
    if (!relationshipData.studentId) {
      throw new Error('StudentTeacher relationship requires studentId');
    }

    if (!relationshipData.teacherId) {
      throw new Error('StudentTeacher relationship requires teacherId');
    }

    console.log('StudentTeacher trigger - processed relationship data:', relationshipData);

    return relationshipData;
  };

  // Handle array of relationships
  if (Array.isArray(query.with)) {
    query.with = query.with.map(processRelationshipData);
  } else {
    // Handle single relationship
    query.with = processRelationshipData(query.with);
  }

  return query;
};

// Trigger for updating student-teacher relationships
export const set: SetTrigger = (query) => {
  // Ensure query.to exists
  if (!query.to) {
    return query;
  }

  console.log('StudentTeacher update trigger - data:', query.to);

  // Log status changes for debugging
  if ('status' in (query.to as any)) {
    console.log('StudentTeacher status change:', (query.to as any).status);
  }

  return query;
};

// Trigger for getting student-teacher relationships
export const get: GetTrigger = (query) => {
  // Add any access control logic here if needed
  return query;
};

// Trigger for removing student-teacher relationships
export const remove: RemoveTrigger = (query) => {
  // Add any validation or cleanup logic for deletions
  console.log('StudentTeacher removal trigger called with query:', query);
  return query;
};

// Trigger to run after relationship creation (synchronous)
export const afterAdd = async (query: any, _multiple: any, _options: any) => {
  const relationshipData = query.with;

  if (!relationshipData) {
    return [];
  }

  console.log('🔥 StudentTeacher afterAdd trigger called:', {
    studentId: relationshipData.studentId,
    teacherId: relationshipData.teacherId,
    status: relationshipData.status
  });

  // Here you could add logic to:
  // 1. Send notifications to the student
  // 2. Update class enrollments
  // 3. Create default assignments
  // 4. Log the relationship creation

  return [];
};

// Trigger to run after relationship update (synchronous)
export const afterSet = async (query: any, _multiple: any, _options: any) => {
  console.log('🔥 StudentTeacher afterSet trigger called with query.to:', query.to);

  // Handle status changes
  if ('status' in (query.to as any)) {
    const newStatus = (query.to as any).status;
    console.log('StudentTeacher relationship status changed to:', newStatus);

    // Here you could add logic to:
    // 1. Send notifications about status changes
    // 2. Update class enrollments
    // 3. Archive/restore student data
    // 4. Log the status change
  }

  return [];
};

// Trigger to run after relationship removal (synchronous)
export const afterRemove = (query: any, _multiple: any, _options: any) => {
  console.log('🗑️ StudentTeacher afterRemove trigger called - relationship removed');

  // Here you could add cleanup logic:
  // 1. Remove student from all classes taught by this teacher
  // 2. Archive assignments and grades
  // 3. Send notification emails
  // 4. Log the relationship removal

  return [];
};
