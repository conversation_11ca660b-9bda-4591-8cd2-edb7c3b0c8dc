// Blade's StoredObject type for blob fields
export interface StoredObject {
  key: string;
  src: string;
  meta: {
    size: number;
    width?: number;
    height?: number;
    type: string;
  };
  placeholder?: {
    base64: string;
  };
}

/**
 * Helper function to get image URL from various formats
 * Handles both string URLs and Blade's StoredObject blob format
 */
export const getImageUrl = (image: string | StoredObject | null | undefined): string | undefined => {
  if (!image) return undefined;

  // Debug logging to understand what we're receiving
  console.log('🔍 getImageUrl processing:', {
    type: typeof image,
    isObject: typeof image === 'object',
    hasKey: typeof image === 'object' && image !== null && 'key' in image,
    hasSrc: typeof image === 'object' && image !== null && 'src' in image,
    src: typeof image === 'object' && image !== null && 'src' in image ? (image as any).src : undefined,
    key: typeof image === 'object' && image !== null && 'key' in image ? (image as any).key : undefined
  });

  // Handle string URLs (legacy or direct URLs)
  if (typeof image === 'string') {
    // Check if it's a JSON string that needs parsing (common with RONIN blob fields)
    if (image.startsWith('{') && image.endsWith('}')) {
      try {
        const parsed = JSON.parse(image);
        // Recursively call with the parsed object
        return getImageUrl(parsed);
      } catch (error) {
        console.warn('Failed to parse JSON string:', error);
      }
    }

    // If it's already a full URL, return as-is
    if (image.startsWith('http://') || image.startsWith('https://') || image.startsWith('data:')) {
      return image;
    }

    // If it's just a filename, construct the RONIN storage URL
    // Try different possible URL formats based on the RONIN token structure
    return `https://storage.ronin.co/spa_rz6sti6byuaj1bot/${image}`;
  }

  // Handle StoredObject format from Blade
  if (typeof image === 'object' && image !== null) {
    // For Blade's StoredObject, the 'src' property should contain the full URL
    if ('src' in image && typeof (image as any).src === 'string') {
      const src = (image as any).src;

      // If src is already a full URL, return it
      if (src.startsWith('https://') || src.startsWith('http://')) {
        console.log('✅ Returning existing full URL:', src);
        return src;
      }

      // If src is just a filename/key, construct the full URL
      const constructedUrl = `https://storage.ronin.co/spa_rz6sti6byuaj1bot/${src}`;
      console.log('🔗 Constructed URL from src:', constructedUrl);
      return constructedUrl;
    }

    // Fallback: check for 'key' property
    if ('key' in image && typeof (image as any).key === 'string') {
      const keyUrl = `https://storage.ronin.co/spa_rz6sti6byuaj1bot/${(image as any).key}`;
      console.log('🔑 Constructed URL from key:', keyUrl);
      return keyUrl;
    }

    // Additional fallback: check for 'url' property
    if ('url' in image && typeof (image as any).url === 'string') {
      return (image as any).url;
    }
  }

  return undefined;
};

/**
 * Helper function to get placeholder image for loading states
 * Returns the base64 placeholder if available from StoredObject
 */
export const getImagePlaceholder = (image: string | StoredObject | null | undefined): string | undefined => {
  if (!image) return undefined;
  if (typeof image === 'object' && 'placeholder' in image && image.placeholder) {
    return image.placeholder.base64;
  }
  return undefined;
};

/**
 * Helper function to check if an image is a StoredObject (blob)
 */
export const isStoredObject = (image: any): image is StoredObject => {
  return image && typeof image === 'object' && 'src' in image && 'key' in image && 'meta' in image;
};
