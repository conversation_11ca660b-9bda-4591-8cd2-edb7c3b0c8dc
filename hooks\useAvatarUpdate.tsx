'use client';
import { useState, useCallback } from 'react';

import { useUnifiedSession } from '../lib/auth-client';

interface UseAvatarUpdateReturn {
  updateAvatar: (file: File | null) => Promise<void>;
  isUpdating: boolean;
  error: string | null;
}

export function useAvatarUpdate(): UseAvatarUpdateReturn {
  const { session, refreshSession } = useUnifiedSession();
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateAvatar = useCallback(async (file: File | null) => {
    if (!session?.user?.id) {
      setError('User not authenticated');
      return;
    }

    setIsUpdating(true);
    setError(null);

    try {
      if (file) {
        // Validate file
        if (!file.type.startsWith('image/')) {
          throw new Error('Please select an image file');
        }

        if (file.size > 5 * 1024 * 1024) {
          throw new Error('Image must be smaller than 5MB');
        }

        // Validate image type
        const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (!supportedTypes.includes(file.type)) {
          throw new Error('Supported formats: JPEG, PNG, WebP');
        }

        console.log('Uploading avatar:', {
          name: file.name,
          size: file.size,
          type: file.type
        });

        // Use server-side endpoint for file upload to RONIN storage
        const formData = new FormData();
        formData.append('file', file);
        formData.append('userId', session.user.id);

        const response = await fetch('/api/upload-avatar', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Upload failed');
        }

        const result = await response.json();
        console.log('✅ Server-side avatar upload successful:', result);

        console.log('✅ Avatar uploaded successfully - Blade will update all reactive queries automatically');
      } else {
        // Remove avatar using server-side endpoint
        const response = await fetch('/api/upload-avatar', {
          method: 'POST',
          body: JSON.stringify({
            userId: session.user.id,
            removeAvatar: true
          }),
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Remove failed');
        }

        console.log('✅ Avatar removed successfully');
      }

      // Wait for the database transaction to fully commit before refreshing session
      // This ensures Better Auth reads the updated data
      console.log('⏳ Waiting for database transaction to commit...');
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Force Better Auth session refresh with cache invalidation
      // This is needed because Better Auth has session caching that doesn't automatically
      // sync with Blade's database updates
      console.log('🔄 Refreshing Better Auth session to sync with updated image data...');

      // Try multiple refresh attempts to ensure we get the updated data
      for (let attempt = 1; attempt <= 3; attempt++) {
        console.log(`🔄 Session refresh attempt ${attempt}/3...`);
        const refreshedSession = await refreshSession();

        // Check if we got the updated image data
        const imageData = refreshedSession?.user?.image as any;
        console.log(`🔍 Attempt ${attempt} image data:`, {
          hasImage: !!imageData,
          imageType: typeof imageData,
          imageSrc: imageData?.src,
          imageKey: imageData?.key
        });

        // If we got updated data with the correct URL format, break
        if (imageData?.src?.includes('spa_rz6sti6byuaj1bot')) {
          console.log('✅ Got updated session data with correct URL format');
          break;
        }

        // Wait before next attempt
        if (attempt < 3) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Avatar update failed';
      setError(errorMessage);
      console.error('Avatar update failed:', err);
    } finally {
      setIsUpdating(false);
    }
  }, [session?.user?.id, refreshSession]);

  return {
    updateAvatar,
    isUpdating,
    error
  };
}

// Hook specifically for getting user initials
export function useUserInitials(name?: string): string {
  if (!name) return 'U';
  
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
}
