'use client';
import { useState, useCallback, useRef, useMemo, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { cn } from '../../lib/utils';
import { Avatar, AvatarImage, AvatarFallback } from './avatar.client';
import { useAvatarUpdate } from '../../hooks/useAvatarUpdate';
import { getImageUrl, type StoredObject } from '../../lib/utils/image';
import { Image } from 'blade/client/components';
import { 
  Upload, 
  X, 
  Camera, 
  Trash2, 
  Check,
  AlertCircle,
  Loader2
} from 'lucide-react';

interface AvatarUploadProps {
  currentImage?: string | StoredObject | null;
  userInitials: string;
  userName: string;
  onImageUpdate?: (imageUrl: string | null) => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
}

interface UploadState {
  isUploading: boolean;
  error: string | null;
  success: boolean;
  preview: string | null;
}

const sizeClasses = {
  sm: 'h-16 w-16',
  md: 'h-24 w-24', 
  lg: 'h-32 w-32'
};

const iconSizeClasses = {
  sm: 'h-4 w-4',
  md: 'h-5 w-5',
  lg: 'h-6 w-6'
};

// Helper function to validate image file
const validateImageFile = (file: File): string | null => {
  // Check file type
  if (!file.type.startsWith('image/')) {
    return 'Please select an image file';
  }
  
  // Check file size (max 5MB)
  if (file.size > 5 * 1024 * 1024) {
    return 'Image must be smaller than 5MB';
  }
  
  // Check supported formats
  const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (!supportedTypes.includes(file.type)) {
    return 'Supported formats: JPEG, PNG, WebP';
  }
  
  return null;
};

// Helper function to create preview URL
const createPreviewUrl = (file: File): string => {
  return URL.createObjectURL(file);
};

export function AvatarUpload({
  currentImage,
  userInitials,
  userName,
  onImageUpdate,
  className,
  size = 'md',
  disabled = false
}: AvatarUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadState, setUploadState] = useState<UploadState>({
    isUploading: false,
    error: null,
    success: false,
    preview: null
  });
  const [isDragOver, setIsDragOver] = useState(false);

  // Use the avatar update hook
  const { updateAvatar, isUpdating, error: updateError } = useAvatarUpdate();

  // Handle successful updates
  const handleUpdateSuccess = useCallback((imageUrl: string | null) => {
    setUploadState(prev => ({
      ...prev,
      isUploading: false,
      success: true,
      error: null,
      preview: null
    }));

    // Call the callback with the new image URL
    onImageUpdate?.(imageUrl);

    // Clear success state after 2 seconds
    setTimeout(() => {
      setUploadState(prev => ({ ...prev, success: false }));
    }, 2000);
  }, [onImageUpdate]);

  // Handle update errors
  const handleUpdateError = useCallback((errorMessage: string) => {
    setUploadState(prev => ({
      ...prev,
      isUploading: false,
      error: errorMessage,
      success: false,
      preview: null
    }));

    // Clear error after 5 seconds
    setTimeout(() => {
      setUploadState(prev => ({ ...prev, error: null }));
    }, 5000);
  }, []);

  // Watch for update errors
  useEffect(() => {
    if (updateError) {
      handleUpdateError(updateError);
    }
  }, [updateError, handleUpdateError]);

  // Handle file selection
  const handleFileSelect = useCallback(async (file: File) => {
    if (disabled) return;
    
    const validationError = validateImageFile(file);
    if (validationError) {
      setUploadState(prev => ({
        ...prev,
        error: validationError,
        preview: null
      }));
      return;
    }

    // Create preview
    const previewUrl = createPreviewUrl(file);
    setUploadState(prev => ({
      ...prev,
      preview: previewUrl,
      error: null,
      success: false,
      isUploading: true
    }));

    // Start upload
    try {
      await updateAvatar(file);
      // Success - clear preview and show success state
      // The actual image URL will be updated via session refresh
      handleUpdateSuccess(null);
    } catch (error) {
      // Error will be handled by the useEffect watching updateError
    }
  }, [disabled, updateAvatar]);

  // Handle file input change
  const handleInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
    // Reset input value to allow selecting the same file again
    event.target.value = '';
  }, [handleFileSelect]);

  // Handle drag and drop
  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
    
    if (disabled) return;
    
    const file = event.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  }, [disabled, handleFileSelect]);

  // Handle remove avatar
  const handleRemoveAvatar = useCallback(async () => {
    if (disabled) return;

    setUploadState(prev => ({
      ...prev,
      isUploading: true,
      error: null,
      success: false,
      preview: null
    }));

    try {
      await updateAvatar(null);
      // Success - show success state
      handleUpdateSuccess(null);
    } catch (error) {
      // Error will be handled by the useEffect watching updateError
    }
  }, [disabled, updateAvatar, handleUpdateSuccess]);

  // Handle click to open file dialog
  const handleClick = useCallback(() => {
    if (disabled || uploadState.isUploading) return;
    fileInputRef.current?.click();
  }, [disabled, uploadState.isUploading]);

  // Determine which image to show for fallback AvatarImage component
  const displayImageUrl = useMemo(() => {
    if (uploadState.preview) return uploadState.preview;
    return getImageUrl(currentImage);
  }, [uploadState.preview, currentImage]);

  const isLoading = uploadState.isUploading || isUpdating;

  return (
    <div className={cn("relative group", className)}>
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleInputChange}
        className="hidden"
        disabled={disabled}
      />

      {/* Avatar container with drag and drop */}
      <div
        className={cn(
          "relative cursor-pointer transition-all duration-200",
          sizeClasses[size],
          isDragOver && "scale-105",
          disabled && "cursor-not-allowed opacity-50"
        )}
        onClick={handleClick}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {/* Avatar */}
        <Avatar className={cn(
          "ring-2 ring-black/20 dark:ring-white/20 transition-all duration-200",
          sizeClasses[size],
          isDragOver && "ring-blue-500 dark:ring-blue-400",
          uploadState.error && "ring-red-500 dark:ring-red-400",
          uploadState.success && "ring-green-500 dark:ring-green-400"
        )}>
          {uploadState.preview ? (
            <AvatarImage
              src={uploadState.preview}
              alt={userName}
              className="object-cover"
            />
          ) : currentImage && typeof currentImage === 'object' ? (
            <Image
              src={currentImage}
              alt={userName}
              width={size === 'sm' ? 64 : size === 'md' ? 96 : 128}
              height={size === 'sm' ? 64 : size === 'md' ? 96 : 128}
              className="h-full w-full object-cover rounded-full"
            />
          ) : displayImageUrl ? (
            <AvatarImage
              src={displayImageUrl}
              alt={userName}
              className="object-cover"
            />
          ) : null}
          <AvatarFallback className="bg-gradient-to-br from-blue-400 via-indigo-500 to-purple-500 dark:from-blue-500 dark:via-indigo-600 dark:to-purple-600 text-white font-manrope_1 font-semibold relative">
            {/* Light mode overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-black/10 dark:hidden" />
            {/* Dark mode overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-black/20 hidden dark:block" />
            {/* Subtle inner glow */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/30 via-transparent to-transparent opacity-50 dark:opacity-30" />
            <span className="relative z-10">{userInitials}</span>
          </AvatarFallback>
        </Avatar>

        {/* Overlay with upload/loading state */}
        <AnimatePresence>
          {(isDragOver || isLoading || (!displayImageUrl && !currentImage && !disabled)) && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className={cn(
                "absolute inset-0 rounded-full flex items-center justify-center",
                isDragOver
                  ? "bg-blue-500/20 dark:bg-blue-400/20"
                  : "bg-black/40 dark:bg-black/60"
              )}
            >
              {isLoading ? (
                <Loader2 className={cn("animate-spin text-white", iconSizeClasses[size])} />
              ) : (
                <Camera className={cn("text-white", iconSizeClasses[size])} />
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Success indicator */}
        <AnimatePresence>
          {uploadState.success && (
            <motion.div
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0, opacity: 0 }}
              className="absolute -top-1 -right-1 bg-green-500 rounded-full p-1"
            >
              <Check className="h-3 w-3 text-white" />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Remove button - only show if there's an image */}
      {(displayImageUrl || currentImage) && !disabled && !isLoading && (
        <motion.button
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0, opacity: 0 }}
          onClick={(e) => {
            e.stopPropagation();
            handleRemoveAvatar();
          }}
          className="absolute -top-1 -left-1 bg-red-500 hover:bg-red-600 rounded-full p-1 transition-colors duration-200 group-hover:scale-110"
        >
          <X className="h-3 w-3 text-white" />
        </motion.button>
      )}

      {/* Error message */}
      <AnimatePresence>
        {uploadState.error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full left-0 right-0 mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
          >
            <div className="flex items-center gap-2 text-sm text-red-600 dark:text-red-400">
              <AlertCircle className="h-4 w-4 flex-shrink-0" />
              <span className="font-medium">{uploadState.error}</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Upload instructions */}
      {!displayImageUrl && !currentImage && !isLoading && !disabled && (
        <div className="absolute top-full left-0 right-0 mt-2 text-center">
          <p className="text-xs text-black/60 dark:text-white/60">
            Click or drag to upload
          </p>
          <p className="text-xs text-black/40 dark:text-white/40">
            JPEG, PNG, WebP • Max 5MB
          </p>
        </div>
      )}
    </div>
  );
}
